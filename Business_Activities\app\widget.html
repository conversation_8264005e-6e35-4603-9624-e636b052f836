<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IFZA Business Activities</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 120px;
            height: auto;
            margin-bottom: 10px;
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .search-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-box {
            width: 100%;
            padding: 12px 20px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            transition: border-color 0.3s;
        }

        .search-box:focus {
            border-color: #3498db;
        }

        .search-placeholder {
            color: #999;
            font-size: 14px;
            margin-top: 8px;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1200px;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 12px 10px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
            font-size: 13px;
            line-height: 1.4;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .activity-code {
            font-weight: bold;
            color: #2c3e50;
            min-width: 80px;
        }

        .activity-name {
            font-weight: 600;
            color: #34495e;
            min-width: 200px;
        }

        .activity-description {
            max-width: 300px;
            word-wrap: break-word;
        }

        .type-professional {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-commercial {
            background: #e3f2fd;
            color: #1565c0;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-regulated {
            background: #ffebee;
            color: #c62828;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-non-regulated {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-top: 1px solid #eee;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 16px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }

            .pagination-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 20px;">
                <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                    <span style="color: white; font-weight: bold; font-size: 20px;">I</span>
                </div>
                <span style="font-size: 24px; font-weight: bold; color: #2c3e50;">IFZA</span>
            </div>
            <h1>Business Activities</h1>
        </div>

        <div class="search-container">
            <input type="text" id="searchInput" class="search-box" placeholder="Search by Activity Code, Name, or Description...">
            <div class="search-placeholder">
                Search across activity codes, names, and descriptions to find specific business activities
            </div>
        </div>

        <div class="table-container">
            <div class="table-wrapper">
                <table id="activitiesTable">
                    <thead>
                        <tr>
                            <th>Activity Code</th>
                            <th>Activity Name</th>
                            <th>Activity Description</th>
                            <th>Type</th>
                            <th>Property Requirements</th>
                            <th>Notes</th>
                            <th>Classification</th>
                            <th>Approval Requirement</th>
                            <th>Approving Entity 1</th>
                            <th>Approving Entity 2</th>
                        </tr>
                    </thead>
                    <tbody id="activitiesTableBody">
                        <tr>
                            <td colspan="10" class="loading">Loading business activities...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination" id="pagination">
                <div class="pagination-info" id="paginationInfo">
                    Showing 0 - 0 of 0 activities
                </div>
                <div class="pagination-controls" id="paginationControls">
                    <!-- Pagination buttons will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script src="business-activities.js"></script>
</body>
</html>Take a picture. 
