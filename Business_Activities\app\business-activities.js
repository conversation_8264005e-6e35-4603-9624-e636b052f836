/**
 * Business Activities Table Management
 * Handles data loading, search functionality, and pagination
 */

class BusinessActivitiesManager {
    constructor() {
        this.activities = [];
        this.filteredActivities = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        
        this.init();
    }

    async init() {
        console.log('=== Initializing Business Activities Manager ===');
        try {
            console.log('Step 1: Loading activities...');
            await this.loadActivities();
            console.log('Step 2: Setting up event listeners...');
            this.setupEventListeners();
            console.log('Step 3: Rendering table...');
            this.renderTable();
            console.log('Step 4: Rendering pagination...');
            this.renderPagination();
            console.log('=== Initialization completed successfully ===');
        } catch (error) {
            console.error('=== ERROR during initialization ===');
            console.error('Error initializing Business Activities Manager:', error);
            this.showError('Failed to load business activities data');
            console.error('=== END INITIALIZATION ERROR ===');
        }
    }

    async loadActivities() {
        console.log('=== Starting loadActivities ===');
        try {
            console.log('Attempting to fetch actitvities.json...');

            // Create a timeout promise
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Fetch timeout after 10 seconds')), 10000);
            });

            // Try to fetch with timeout
            const fetchPromise = fetch('actitvities.json');
            console.log('Fetch promise created, waiting for response...');

            const response = await Promise.race([fetchPromise, timeoutPromise]);
            console.log('Got response:', response);
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            console.log('Starting to parse JSON...');
            const data = await response.json();
            console.log('JSON parsed successfully');
            console.log('Raw data type:', typeof data);
            console.log('Is array:', Array.isArray(data));

            if (Array.isArray(data)) {
                console.log('Data length:', data.length);
                console.log('First item:', data[0]);
            }

            if (!Array.isArray(data)) {
                throw new Error('JSON data is not an array');
            }

            this.activities = data;
            this.filteredActivities = [...this.activities];
            console.log(`Successfully loaded ${this.activities.length} activities`);
            console.log('=== loadActivities completed successfully ===');
        } catch (error) {
            console.error('=== ERROR in loadActivities ===');
            console.error('Error type:', error.constructor.name);
            console.error('Error message:', error.message);
            console.error('Full error:', error);
            console.error('=== END ERROR ===');
            throw error;
        }
    }

    setupEventListeners() {
        const searchInput = document.getElementById('searchInput');
        
        // Debounced search to avoid excessive filtering
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.handleSearch(e.target.value);
            }, 300);
        });

        // Handle Enter key for immediate search
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                clearTimeout(searchTimeout);
                this.handleSearch(e.target.value);
            }
        });
    }

    handleSearch(searchTerm) {
        this.searchTerm = searchTerm.toLowerCase().trim();
        this.currentPage = 1; // Reset to first page when searching
        
        if (this.searchTerm === '') {
            this.filteredActivities = [...this.activities];
        } else {
            this.filteredActivities = this.activities.filter(activity => {
                return (
                    activity.actcode.toString().includes(this.searchTerm) ||
                    activity.name.toLowerCase().includes(this.searchTerm) ||
                    activity.desc.toLowerCase().includes(this.searchTerm)
                );
            });
        }
        
        this.renderTable();
        this.renderPagination();
    }

    renderTable() {
        const tableBody = document.getElementById('activitiesTableBody');
        
        if (this.filteredActivities.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="no-results">
                        ${this.searchTerm ? 'No activities found matching your search criteria.' : 'No activities available.'}
                    </td>
                </tr>
            `;
            return;
        }

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageActivities = this.filteredActivities.slice(startIndex, endIndex);

        tableBody.innerHTML = pageActivities.map(activity => `
            <tr>
                <td class="activity-code">${activity.actcode}</td>
                <td class="activity-name">${this.escapeHtml(activity.name)}</td>
                <td class="activity-description">${this.escapeHtml(activity.desc)}</td>
                <td>
                    <span class="type-${activity.type.toLowerCase()}">
                        ${activity.type}
                    </span>
                </td>
                <td>${this.escapeHtml(activity.prop || '')}</td>
                <td>${this.escapeHtml(activity.notes || '')}</td>
                <td>
                    <span class="status-${activity.status.toLowerCase().replace(/[^a-z]/g, '-')}">
                        ${activity.status}
                    </span>
                </td>
                <td>${this.escapeHtml(activity.tlapp || '')}</td>
                <td>${this.escapeHtml(activity.appr1 || '')}</td>
                <td>${this.escapeHtml(activity.appr2 || '')}</td>
            </tr>
        `).join('');
    }

    renderPagination() {
        const totalItems = this.filteredActivities.length;
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        // Update pagination info
        const startItem = totalItems === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, totalItems);
        
        document.getElementById('paginationInfo').textContent = 
            `Showing ${startItem} - ${endItem} of ${totalItems} activities`;

        // Generate pagination controls
        const paginationControls = document.getElementById('paginationControls');
        
        if (totalPages <= 1) {
            paginationControls.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="businessActivitiesManager.goToPage(${this.currentPage - 1})">
                Previous
            </button>
        `;

        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        // Adjust start page if we're near the end
        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page and ellipsis
        if (startPage > 1) {
            paginationHTML += `
                <button class="pagination-btn" onclick="businessActivitiesManager.goToPage(1)">1</button>
            `;
            if (startPage > 2) {
                paginationHTML += `<span style="padding: 0 8px;">...</span>`;
            }
        }

        // Page number buttons
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="businessActivitiesManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        // Last page and ellipsis
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span style="padding: 0 8px;">...</span>`;
            }
            paginationHTML += `
                <button class="pagination-btn" onclick="businessActivitiesManager.goToPage(${totalPages})">
                    ${totalPages}
                </button>
            `;
        }

        // Next button
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
                    onclick="businessActivitiesManager.goToPage(${this.currentPage + 1})">
                Next
            </button>
        `;

        paginationControls.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredActivities.length / this.itemsPerPage);
        
        if (page < 1 || page > totalPages) {
            return;
        }
        
        this.currentPage = page;
        this.renderTable();
        this.renderPagination();
        
        // Scroll to top of table
        document.getElementById('activitiesTable').scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        const tableBody = document.getElementById('activitiesTableBody');
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="no-results" style="color: #e74c3c;">
                    <strong>Error:</strong> ${message}<br>
                    <small>Check browser console for more details</small>
                </td>
            </tr>
        `;

        // Also update pagination info
        document.getElementById('paginationInfo').textContent = 'Error loading data';
    }
}

// Initialize the Business Activities Manager when the DOM is loaded
let businessActivitiesManager;

console.log('Script loaded, waiting for DOM...');

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, creating BusinessActivitiesManager...');
    try {
        businessActivitiesManager = new BusinessActivitiesManager();
        console.log('BusinessActivitiesManager created successfully');
    } catch (error) {
        console.error('Error creating BusinessActivitiesManager:', error);
    }
});

console.log('Event listener added');

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BusinessActivitiesManager;
}
