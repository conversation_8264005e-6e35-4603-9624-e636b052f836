/**
 * Business Activities Table Management
 * Handles data loading, search functionality, and pagination
 */

class BusinessActivitiesManager {
    constructor() {
        this.activities = [];
        this.filteredActivities = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.searchTerm = '';
        
        this.init();
    }

    async init() {
        try {
            await this.loadActivities();
            this.setupEventListeners();
            this.renderTable();
            this.renderPagination();
        } catch (error) {
            console.error('Error initializing Business Activities Manager:', error);
            this.showError('Failed to load business activities data');
        }
    }

    async loadActivities() {
        try {
            console.log('Attempting to fetch actitvities.json...');
            // Try different paths to find the JSON file
            const possiblePaths = [
                'actitvities.json',
                './actitvities.json',
                '/app/actitvities.json'
            ];

            let response;
            let lastError;

            for (const path of possiblePaths) {
                try {
                    console.log(`Trying path: ${path}`);
                    response = await fetch(path);
                    console.log(`Response for ${path}:`, response.status);
                    if (response.ok) {
                        break;
                    }
                } catch (err) {
                    console.log(`Failed to fetch from ${path}:`, err.message);
                    lastError = err;
                }
            }

            if (!response || !response.ok) {
                throw new Error(`Could not load JSON file. Last error: ${lastError?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            console.log('Raw data type:', typeof data);
            console.log('Data length:', Array.isArray(data) ? data.length : 'Not an array');

            if (!Array.isArray(data)) {
                throw new Error('JSON data is not an array');
            }

            this.activities = data;
            this.filteredActivities = [...this.activities];
            console.log(`Successfully loaded ${this.activities.length} activities`);
        } catch (error) {
            console.error('Detailed error loading activities:', error);
            throw error;
        }
    }

    setupEventListeners() {
        const searchInput = document.getElementById('searchInput');
        
        // Debounced search to avoid excessive filtering
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.handleSearch(e.target.value);
            }, 300);
        });

        // Handle Enter key for immediate search
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                clearTimeout(searchTimeout);
                this.handleSearch(e.target.value);
            }
        });
    }

    handleSearch(searchTerm) {
        this.searchTerm = searchTerm.toLowerCase().trim();
        this.currentPage = 1; // Reset to first page when searching
        
        if (this.searchTerm === '') {
            this.filteredActivities = [...this.activities];
        } else {
            this.filteredActivities = this.activities.filter(activity => {
                return (
                    activity.actcode.toString().includes(this.searchTerm) ||
                    activity.name.toLowerCase().includes(this.searchTerm) ||
                    activity.desc.toLowerCase().includes(this.searchTerm)
                );
            });
        }
        
        this.renderTable();
        this.renderPagination();
    }

    renderTable() {
        const tableBody = document.getElementById('activitiesTableBody');
        
        if (this.filteredActivities.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="no-results">
                        ${this.searchTerm ? 'No activities found matching your search criteria.' : 'No activities available.'}
                    </td>
                </tr>
            `;
            return;
        }

        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageActivities = this.filteredActivities.slice(startIndex, endIndex);

        tableBody.innerHTML = pageActivities.map(activity => `
            <tr>
                <td class="activity-code">${activity.actcode}</td>
                <td class="activity-name">${this.escapeHtml(activity.name)}</td>
                <td class="activity-description">${this.escapeHtml(activity.desc)}</td>
                <td>
                    <span class="type-${activity.type.toLowerCase()}">
                        ${activity.type}
                    </span>
                </td>
                <td>${this.escapeHtml(activity.prop || '')}</td>
                <td>${this.escapeHtml(activity.notes || '')}</td>
                <td>
                    <span class="status-${activity.status.toLowerCase().replace(/[^a-z]/g, '-')}">
                        ${activity.status}
                    </span>
                </td>
                <td>${this.escapeHtml(activity.tlapp || '')}</td>
                <td>${this.escapeHtml(activity.appr1 || '')}</td>
                <td>${this.escapeHtml(activity.appr2 || '')}</td>
            </tr>
        `).join('');
    }

    renderPagination() {
        const totalItems = this.filteredActivities.length;
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        
        // Update pagination info
        const startItem = totalItems === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, totalItems);
        
        document.getElementById('paginationInfo').textContent = 
            `Showing ${startItem} - ${endItem} of ${totalItems} activities`;

        // Generate pagination controls
        const paginationControls = document.getElementById('paginationControls');
        
        if (totalPages <= 1) {
            paginationControls.innerHTML = '';
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="businessActivitiesManager.goToPage(${this.currentPage - 1})">
                Previous
            </button>
        `;

        // Page numbers
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
        
        // Adjust start page if we're near the end
        if (endPage - startPage < maxVisiblePages - 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // First page and ellipsis
        if (startPage > 1) {
            paginationHTML += `
                <button class="pagination-btn" onclick="businessActivitiesManager.goToPage(1)">1</button>
            `;
            if (startPage > 2) {
                paginationHTML += `<span style="padding: 0 8px;">...</span>`;
            }
        }

        // Page number buttons
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="businessActivitiesManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        // Last page and ellipsis
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += `<span style="padding: 0 8px;">...</span>`;
            }
            paginationHTML += `
                <button class="pagination-btn" onclick="businessActivitiesManager.goToPage(${totalPages})">
                    ${totalPages}
                </button>
            `;
        }

        // Next button
        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
                    onclick="businessActivitiesManager.goToPage(${this.currentPage + 1})">
                Next
            </button>
        `;

        paginationControls.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredActivities.length / this.itemsPerPage);
        
        if (page < 1 || page > totalPages) {
            return;
        }
        
        this.currentPage = page;
        this.renderTable();
        this.renderPagination();
        
        // Scroll to top of table
        document.getElementById('activitiesTable').scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        const tableBody = document.getElementById('activitiesTableBody');
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="no-results" style="color: #e74c3c;">
                    <strong>Error:</strong> ${message}<br>
                    <small>Check browser console for more details</small>
                </td>
            </tr>
        `;

        // Also update pagination info
        document.getElementById('paginationInfo').textContent = 'Error loading data';
    }
}

// Initialize the Business Activities Manager when the DOM is loaded
let businessActivitiesManager;

document.addEventListener('DOMContentLoaded', () => {
    businessActivitiesManager = new BusinessActivitiesManager();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BusinessActivitiesManager;
}
