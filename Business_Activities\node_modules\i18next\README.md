# i18next: learn once - translate everywhere [![Tweet](https://img.shields.io/twitter/url/http/shields.io.svg?style=social)](https://twitter.com/intent/tweet?text=Awesome%20i18next:%20learn%20once%20-%20translate%20everywhere%20-%20the%20internationalization%20ecosystem%20&url=https://github.com/i18next/i18next&via=jamuhl&hashtags=i18n,javascript,dev)

[![Travis](https://img.shields.io/travis/i18next/i18next/master.svg?style=flat-square)](https://travis-ci.org/i18next/i18next)
[![Code Climate](https://codeclimate.com/github/codeclimate/codeclimate/badges/gpa.svg)](https://codeclimate.com/github/i18next/i18next)
[![Coveralls](https://img.shields.io/coveralls/i18next/i18next/master.svg?style=flat-square)](https://coveralls.io/github/i18next/i18next)
[![Package Quality](http://npm.packagequality.com/shield/i18next.svg)](http://packagequality.com/#?package=i18next)
[![cdnjs version](https://img.shields.io/cdnjs/v/i18next.svg?style=flat-square)](https://cdnjs.com/libraries/i18next)
[![npm version](https://img.shields.io/npm/v/i18next.svg?style=flat-square)](https://www.npmjs.com/package/i18next)
[![Bower](https://img.shields.io/bower/v/i18next.svg?style=flat-square)]()
[![David](https://img.shields.io/david/i18next/i18next.svg?style=flat-square)](https://david-dm.org/i18next/i18next)

i18next is a very popular internationalization framework for browser or any other javascript environment (eg. node.js).

![ecosystem](https://www.i18next.com/assets/img/i18next-ecosystem.jpg)


i18next provides:

- Flexible connection to [backend](https://www.i18next.com/plugins-and-utils.html#backends) (loading translations via xhr, ...)
- Optional [caching](https://www.i18next.com/plugins-and-utils.html#caches), user [language detection](https://www.i18next.com/plugins-and-utils.html#language-detector), ...
- Proper [pluralizations](https://www.i18next.com/plurals.html)
- Translation [context](https://www.i18next.com/context.html)
- [Nesting](https://www.i18next.com/nesting.html), [Variable replacement](https://www.i18next.com/interpolation.html)
- Flexibility: [Use it anywhere](https://www.i18next.com/supported-frameworks.html)
- Extensibility: eg. [sprintf](https://www.i18next.com/plugins-and-utils.html#post-processors)
- ...


For more information visit the website:

- [Getting started](https://www.i18next.com/getting-started.html)
- [Translation Functionality](https://www.i18next.com/essentials.html)
- [API](https://www.i18next.com/api.html)


Our focus is providing the core to building a booming ecosystem. Independent of the building blocks you choose, be it react, angular or even good old jquery proper translation capabilities are just [one step away](https://www.i18next.com/supported-frameworks.html).

--------------
**NEWS: localization as a service - locize.com**

Having done a big rewrite of i18next in spring we are proud to announce the next big step to get your webproject translated with less effort. We just released [locize](http://locize.com/?utm_source=i18next_readme&utm_medium=github) a translation management system built around the i18next ecosystem.

![locize](https://www.i18next.com/assets/img/locize_recap_big_low.gif)

With using [locize](http://locize.com/?utm_source=i18next_readme&utm_medium=github) you directly support the future of i18next.

--------------
