{"name": "i18next", "version": "10.6.0", "description": "i18next internationalization framework", "main": "./index.js", "jsnext:main": "dist/es/index.js", "module": "dist/es/index.js", "keywords": ["i18next", "internationalization", "i18n", "translation", "localization", "l10n", "globalization", "gettext"], "homepage": "http://i18next.com", "bugs": "https://github.com/i18next/i18next/issues", "repository": {"type": "git", "url": "https://github.com/i18next/i18next.git"}, "dependencies": {}, "devDependencies": {"babel-cli": "6.22.2", "babel-core": "6.22.1", "babel-eslint": "7.1.1", "babel-plugin-external-helpers": "6.22.0", "babel-plugin-transform-es2015-classes": "6.22.0", "babel-plugin-transform-proto-to-assign": "6.22.0", "babel-preset-es2015": "6.22.0", "babel-preset-stage-0": "6.22.0", "babelify": "7.3.0", "browserify": "14.0.0", "browserify-istanbul": "2.0.0", "chai": "3.5.0", "coveralls": "2.11.16", "eslint": "3.15.0", "eslint-config-airbnb": "14.1.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "4.0.0", "eslint-plugin-react": "6.9.0", "i18next-browser-languagedetector": "1.0.1", "i18next-localstorage-cache": "0.3.0", "i18next-sprintf-postprocessor": "0.2.2", "i18next-xhr-backend": "1.3.0", "istanbul": "gotwarlost/istanbul#source-map", "karma": "1.4.1", "karma-browserify": "5.1.1", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.0.0", "karma-cli": "1.0.1", "karma-coverage": "douglasduteil/karma-coverage#next", "karma-coveralls": "1.1.2", "karma-expect": "1.1.3", "karma-mocha": "1.3.0", "karma-phantomjs-launcher": "1.0.2", "karma-rollup-preprocessor": "3.0.3", "karma-sinon": "1.0.5", "karma-spec-reporter": "0.0.26", "mkdirp": "0.5.1", "mocha": "3.2.0", "phantomjs-prebuilt": "2.1.14", "rimraf": "2.5.4", "rollup": "0.41.4", "rollup-plugin-babel": "2.7.1", "rollup-plugin-node-resolve": "2.0.0", "rollup-plugin-uglify": "1.0.1", "sinon": "1.17.7", "watchify": "3.9.0", "yargs": "6.6.0"}, "scripts": {"test": "npm run test:new && npm run test:compat", "test:new": "karma start karma.conf.js --singleRun", "test:compat": "karma start karma.backward.conf.js --singleRun", "tdd": "karma start karma.conf.js", "tdd:compat": "karma start karma.backward.conf.js", "clean": "rimraf dist && mkdirp dist", "copy": "cp ./dist/umd/i18next.min.js ./i18next.min.js && cp ./dist/umd/i18next.js ./i18next.js", "build:es": "BABEL_ENV=jsnext babel src --out-dir dist/es", "build:cjs": "babel src --out-dir dist/commonjs", "build:umd": "rollup -c rollup.config.js --format umd && rollup -c rollup.config.js --format umd --uglify", "build:amd": "rollup -c rollup.config.js --format amd && rollup -c rollup.config.js --format umd --uglify", "build:iife": "rollup -c rollup.config.js --format iife && rollup -c rollup.config.js --format iife --uglify", "build": "npm run clean && npm run build:cjs && npm run build:es && npm run build:umd && npm run copy", "preversion": "npm run test && npm run build && git push", "postversion": "git push && git push --tags"}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT"}