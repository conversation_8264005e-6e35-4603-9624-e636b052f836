{"name": "setprot<PERSON>of", "version": "1.1.0", "description": "A small polyfill for Object.setprototypeof", "main": "index.js", "typings": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/wesleytodd/setprototypeof.git"}, "keywords": ["polyfill", "object", "setprot<PERSON>of"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/wesleytodd/setprototypeof/issues"}, "homepage": "https://github.com/wesleytodd/setprototypeof"}