{"name": "cookie-signature", "version": "1.0.6", "description": "Sign and unsign cookies", "keywords": ["cookie", "sign", "unsign"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/node-cookie-signature.git"}, "dependencies": {}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "mocha --require should --reporter spec"}, "main": "index"}